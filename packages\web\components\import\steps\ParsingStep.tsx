'use client';

import { useState } from 'react';
import { useImport } from '@/contexts/ImportContext';
import { useImportStatus } from '@/hooks/useImportStatus';
import { Card, CardContent } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Button } from '@/components/ui/button';
import {
  Brain,
  FileText,
  MapPin,
  Calendar,
  Clock,
  AlertCircle,
  CheckCircle,
  Loader2,
  RefreshCw
} from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { useImportAnalytics } from '@/hooks/useImportAnalytics';

interface ParseStep {
  id: string;
  label: string;
  icon: React.ComponentType<{ className?: string }>;
  description: string;
}

const PARSE_STEPS: ParseStep[] = [
  {
    id: 'initializing',
    label: 'Initializing',
    icon: Brain,
    description: 'Setting up AI parsing session'
  },
  {
    id: 'extracting',
    label: 'Extracting Content',
    icon: FileText,
    description: 'Reading and processing your content'
  },
  {
    id: 'parsing',
    label: 'AI Analysis',
    icon: Brain,
    description: 'AI is analyzing your travel plans'
  },
  {
    id: 'locations',
    label: 'Finding Locations',
    icon: MapPin,
    description: 'Identifying destinations and places'
  },
  {
    id: 'dates',
    label: 'Processing Dates',
    icon: Calendar,
    description: 'Organizing timeline and schedule'
  },
  {
    id: 'finalizing',
    label: 'Finalizing',
    icon: CheckCircle,
    description: 'Completing your itinerary'
  }
];



export function ParsingStep() {
  const {
    importId,
    setParsedTrip,
    setStep,
    setError
  } = useImport();

  const [currentStepIndex, setCurrentStepIndex] = useState(0);
  const { trackParsingProgress, trackImportError } = useImportAnalytics();

  const {
    status,
    progress,
    currentStep,
    result,
    error,
    isPolling,
    timeElapsed,
    retryCount,
    retry
  } = useImportStatus({
    importId,
    onComplete: (result) => {
      setParsedTrip(result);
      setStep('preview');
    },
    onError: (error) => {
      trackImportError(error, 'parsing', {
        importId,
        timeElapsed
      });
      setError(error);
      setStep('input');
    },
    onProgress: (progress, step) => {
      trackParsingProgress(progress, step);
      const stepIndex = PARSE_STEPS.findIndex(s => s.id === step);
      if (stepIndex !== -1) {
        setCurrentStepIndex(stepIndex);
      }
    }
  });

  // Handle error state
  if (error) {
    setError(error);
    setStep('input');
    return null;
  }

  const formatTime = (ms: number): string => {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;

    if (minutes > 0) {
      return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
    }
    return `${remainingSeconds}s`;
  };

  const currentStepData = PARSE_STEPS[currentStepIndex];
  const overallProgress = ((currentStepIndex + 1) / PARSE_STEPS.length) * 100;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center">
        <motion.div
          initial={{ scale: 0.8, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          className="inline-flex items-center justify-center w-16 h-16 bg-blue-100 rounded-full mb-4"
        >
          <Brain className="w-8 h-8 text-blue-600" />
        </motion.div>

        <h2 className="text-2xl font-bold text-gray-900 mb-2">
          AI is analyzing your travel plans
        </h2>

        <p className="text-gray-600">
          This usually takes 30-60 seconds. We're extracting destinations, activities, and dates.
        </p>

        <div className="flex items-center justify-center gap-4 mt-4 text-sm text-gray-500">
          <div className="flex items-center gap-1">
            <Clock className="w-4 h-4" />
            <span>{formatTime(timeElapsed)}</span>
          </div>
          {retryCount > 0 && (
            <div className="flex items-center gap-1">
              <RefreshCw className="w-4 h-4" />
              <span>Retry {retryCount}/3</span>
            </div>
          )}
        </div>
      </div>

      {/* Progress Card */}
      <Card>
        <CardContent className="p-6">
          {/* Overall Progress */}
          <div className="mb-6">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-gray-700">
                Overall Progress
              </span>
              <span className="text-sm text-gray-500">
                {Math.round(overallProgress)}%
              </span>
            </div>
            <Progress value={overallProgress} className="h-2" />
          </div>

          {/* Current Step */}
          <div className="mb-6">
            <div className="flex items-center gap-3 mb-4">
              <div className="flex items-center justify-center w-10 h-10 bg-blue-100 rounded-full">
                {isPolling ? (
                  <Loader2 className="w-5 h-5 text-blue-600 animate-spin" />
                ) : (
                  <currentStepData.icon className="w-5 h-5 text-blue-600" />
                )}
              </div>
              <div>
                <h3 className="font-medium text-gray-900">
                  {currentStepData.label}
                </h3>
                <p className="text-sm text-gray-600">
                  {currentStepData.description}
                </p>
              </div>
            </div>
          </div>

          {/* Step List */}
          <div className="space-y-3">
            {PARSE_STEPS.map((step, index) => {
              const isCompleted = index < currentStepIndex;
              const isCurrent = index === currentStepIndex;
              const isPending = index > currentStepIndex;

              return (
                <motion.div
                  key={step.id}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className={`flex items-center gap-3 p-3 rounded-lg transition-colors ${
                    isCurrent
                      ? 'bg-blue-50 border border-blue-200'
                      : isCompleted
                        ? 'bg-green-50'
                        : 'bg-gray-50'
                  }`}
                >
                  <div className={`flex items-center justify-center w-8 h-8 rounded-full ${
                    isCompleted
                      ? 'bg-green-100 text-green-600'
                      : isCurrent
                        ? 'bg-blue-100 text-blue-600'
                        : 'bg-gray-100 text-gray-400'
                  }`}>
                    {isCompleted ? (
                      <CheckCircle className="w-4 h-4" />
                    ) : isCurrent && isPolling ? (
                      <Loader2 className="w-4 h-4 animate-spin" />
                    ) : (
                      <step.icon className="w-4 h-4" />
                    )}
                  </div>

                  <div className="flex-1">
                    <div className={`text-sm font-medium ${
                      isCompleted
                        ? 'text-green-700'
                        : isCurrent
                          ? 'text-blue-700'
                          : 'text-gray-500'
                    }`}>
                      {step.label}
                    </div>
                    <div className={`text-xs ${
                      isCompleted
                        ? 'text-green-600'
                        : isCurrent
                          ? 'text-blue-600'
                          : 'text-gray-400'
                    }`}>
                      {step.description}
                    </div>
                  </div>
                </motion.div>
              );
            })}
          </div>

          {/* Retry Button */}
          {timeElapsed > 60000 && ( // Show after 1 minute
            <div className="mt-6 pt-6 border-t border-gray-200">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2 text-amber-600">
                  <AlertCircle className="w-4 h-4" />
                  <span className="text-sm">Taking longer than usual</span>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={retry}
                  disabled={isPolling}
                  className="text-blue-600 border-blue-200 hover:bg-blue-50"
                >
                  {isPolling ? (
                    <>
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      Retrying...
                    </>
                  ) : (
                    <>
                      <RefreshCw className="w-4 h-4 mr-2" />
                      Retry
                    </>
                  )}
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Tips */}
      <Card className="bg-blue-50 border-blue-200">
        <CardContent className="p-4">
          <div className="flex items-start gap-3">
            <Brain className="w-5 h-5 text-blue-600 mt-0.5" />
            <div>
              <h4 className="font-medium text-blue-900 mb-1">
                What's happening?
              </h4>
              <p className="text-sm text-blue-700">
                Our AI is carefully reading through your content to extract destinations,
                activities, dates, and other travel details. Complex itineraries may take
                a bit longer to process accurately.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
