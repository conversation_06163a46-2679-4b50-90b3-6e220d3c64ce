import type { ParsedTrip } from '@travelviz/shared';
import { useAuthStore } from '@/stores/auth.store';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';

export interface ParseTextRequest {
  content: string;
  source: 'chatgpt' | 'claude' | 'gemini' | 'unknown';
}

export interface ParseTextResponse {
  importId: string;
  estimatedTime?: number;
}

export interface PDFUploadResponse {
  sessionId: string;
  sseUrl: string;
  source: 'chatgpt' | 'claude' | 'gemini' | 'unknown';
  metadata: {
    pageCount: number;
    title?: string;
    author?: string;
    subject?: string;
    keywords?: string;
    creationDate?: Date;
    modificationDate?: Date;
  };
  estimatedTime?: number;
}

export interface CreateTripFromImportResponse {
  tripId: string;
  slug?: string;
  shareUrl?: string;
}

export interface ParseStatusResponse {
  status: 'pending' | 'processing' | 'completed' | 'failed';
  result?: ParsedTrip;
  error?: string;
  progress?: number;
}

async function getAuthHeaders(): Promise<HeadersInit> {
  const accessToken = useAuthStore.getState().accessToken;
  
  if (accessToken) {
    return {
      'Authorization': `Bearer ${accessToken}`,
    };
  }
  
  return {};
}

export const importApi = {
  async parseText(content: string, source: 'chatgpt' | 'claude' | 'gemini' | 'unknown'): Promise<ParseTextResponse> {
    const authHeaders = await getAuthHeaders();
    
    const response = await fetch(`${API_BASE_URL}/api/v1/import/parse-simple`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...authHeaders
      },
      body: JSON.stringify({
        content,
        source
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'Failed to parse conversation');
    }

    const data = await response.json();
    return { importId: data.data.importId };
  },

  async uploadPDF(file: File): Promise<PDFUploadResponse> {
    const authHeaders = await getAuthHeaders();
    
    const formData = new FormData();
    formData.append('file', file);
    
    const response = await fetch(`${API_BASE_URL}/api/v1/import/pdf`, {
      method: 'POST',
      headers: {
        ...authHeaders
        // Note: Don't set Content-Type for FormData, browser will set it with boundary
      },
      body: formData,
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'Failed to upload PDF');
    }

    const data = await response.json();
    return data.data;
  },

  async getParseStatus(importId: string): Promise<ParseStatusResponse> {
    const authHeaders = await getAuthHeaders();
    
    const response = await fetch(`${API_BASE_URL}/api/v1/import/parse-simple/${importId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        ...authHeaders
      },
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'Failed to get parse status');
    }

    const data = await response.json();
    return data.data;
  },

  async createTripFromImport(importId: string): Promise<CreateTripFromImportResponse> {
    const authHeaders = await getAuthHeaders();
    
    const response = await fetch(`${API_BASE_URL}/api/v1/import/parse-simple/${importId}/create-trip`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...authHeaders
      },
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'Failed to create trip');
    }

    const data = await response.json();
    return data.data;
  }
};