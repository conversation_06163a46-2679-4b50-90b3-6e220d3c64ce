import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import axios from 'axios';

// Set up environment before any imports
process.env.OPENROUTER_API_KEY = 'test-api-key';
process.env.UPSTASH_REDIS_REST_URL = 'test-url';
process.env.UPSTASH_REDIS_REST_TOKEN = 'test-token';

// Mock dependencies before imports
vi.mock('axios');
vi.mock('../../config/redis', () => ({
  redis: {
    publish: vi.fn().mockResolvedValue(1),
    get: vi.fn().mockResolvedValue(null),
    set: vi.fn().mockResolvedValue('OK'),
    expire: vi.fn().mockResolvedValue(1),
  },
}));
vi.mock('../redis-connection-pool.service', () => ({
  redisConnectionPool: {
    acquire: vi.fn(),
    execute: vi.fn(),
    healthCheck: vi.fn().mockResolvedValue(true),
  },
}));
vi.mock('../cache.service', () => ({
  cacheService: {
    set: vi.fn(),
    get: vi.fn(),
  },
}));
vi.mock('../geocoding.service', () => ({
  geocodingService: {
    isAvailable: vi.fn(),
    geocodeBatch: vi.fn(),
  },
}));
vi.mock('../../lib/supabase');
vi.mock('../../utils/logger', () => ({
  logger: {
    error: vi.fn(),
    warn: vi.fn(),
    info: vi.fn(),
    debug: vi.fn(),
  },
}));
vi.mock('../aiRouter.service', () => ({
  aiRouter: {
    selectModel: vi.fn().mockReturnValue({
      modelId: 'test-model',
      config: { id: 'test-model' }
    })
  }
}));

// Mock the ParsedTripSchema to avoid validation errors
vi.mock('@travelviz/shared', async () => {
  const actual = await vi.importActual('@travelviz/shared');
  return {
    ...actual,
    ParsedTripSchema: {
      parse: (data: any) => data // Just return the data without validation
    }
  };
});

// Mock validateAIConfig to prevent errors
vi.mock('../../config/ai.config', () => ({
  validateAIConfig: vi.fn(),
  getModelConfig: vi.fn((modelKey: string) => ({ 
    id: 'test-model-id', 
    name: 'Test Model', 
    provider: 'openrouter',
    maxTokens: 4000,
    costPer1kTokens: 0,
    speedRating: 8,
    qualityRating: 8,
  })),
  selectOptimalModel: vi.fn(() => ({
    id: 'optimal-model-id',
    name: 'Optimal Test Model',
    provider: 'openrouter',
    maxTokens: 4000,
    costPer1kTokens: 0,
    speedRating: 9,
    qualityRating: 9,
  })),
  AI_CONFIG: {
    apiKey: 'test-api-key',
    baseURL: 'https://openrouter.ai/api/v1',
    primaryModel: 'test-model',
    fallbackModels: ['fallback-1', 'fallback-2'],
    parsing: {
      maxTokens: 4000,
      temperature: 0.3,
      timeout: 30000,
      retryAttempts: 2,
      retryDelay: 1000,
      enableFallback: true,
    },
    progressMessages: {
      initializing: 'Initializing...',
      extracting: 'Extracting...',
      parsing: 'Parsing...',
      enhancing: 'Enhancing...',
      validating: 'Validating...',
      finalizing: 'Finalizing...',
    },
    systemPrompt: 'Test prompt',
    headers: {
      'HTTP-Referer': 'https://test.com',
      'X-Title': 'Test',
    },
  },
}));

// Now import after mocks are set up
import { AIParserService } from './ai-parser.service';
import { redisConnectionPool } from './redis-connection-pool.service';
import { cacheService } from './cache.service';
import { geocodingService } from './geocoding.service';
import { getSupabaseClient } from '../lib/supabase';
import { logger } from '../utils/logger';
import { redis } from '../config/redis';

describe('AIParserService', () => {
  let service: AIParserService;
  let mockSupabase: any;
  let mockRedis: any;

  beforeEach(() => {
    vi.clearAllMocks();
    
    // Mock Supabase client
    mockSupabase = {
      from: vi.fn().mockReturnThis(),
      insert: vi.fn().mockReturnThis(),
      update: vi.fn().mockReturnThis(),
      select: vi.fn().mockReturnThis(),
      eq: vi.fn().mockReturnThis(),
      single: vi.fn().mockResolvedValue({ data: null, error: null }),
    };
    vi.mocked(getSupabaseClient).mockReturnValue(mockSupabase);

    // Mock Redis
    mockRedis = {
      publish: vi.fn().mockResolvedValue(1),
      get: vi.fn().mockResolvedValue(null),
      set: vi.fn().mockResolvedValue('OK'),
      expire: vi.fn().mockResolvedValue(1),
    };
    vi.mocked(redisConnectionPool).acquire.mockResolvedValue(mockRedis);
    vi.mocked(redisConnectionPool).execute.mockImplementation(async (callback) => {
      return await callback(mockRedis);
    });

    // Mock cache service
    vi.mocked(cacheService).set.mockResolvedValue(true);

    // Mock logger
    vi.mocked(logger).error.mockReturnValue(undefined);
    vi.mocked(logger).info.mockReturnValue(undefined);
    vi.mocked(logger).debug.mockReturnValue(undefined);

    service = new AIParserService();
  });

  describe('createParseSession', () => {
    it('should create a session and start async parsing', async () => {
      const content = 'Test conversation content';
      const source = 'chatgpt';
      const userId = 'user-123';

      mockSupabase.single.mockResolvedValueOnce({ data: {}, error: null });

      const sessionId = await service.createParseSession(content, source, userId);

      expect(sessionId).toMatch(/^[a-f0-9-]+$/); // UUID format
      expect(mockSupabase.from).toHaveBeenCalledWith('ai_import_logs');
      expect(mockSupabase.insert).toHaveBeenCalledWith(expect.objectContaining({
        id: sessionId,
        user_id: userId,
        ai_platform: source,
        import_status: 'processing',
        raw_conversation: content.substring(0, 5000),
        created_at: expect.any(String),
      }));
    });

    it('should throw error when database insert fails', async () => {
      // Override the default mock to return an error
      mockSupabase.from.mockReturnValueOnce({
        insert: vi.fn().mockReturnValue({
          error: { message: 'Database error' },
          data: null
        })
      });

      await expect(
        service.createParseSession('content', 'source', 'user-id')
      ).rejects.toThrow('Failed to create parse session');

      expect(logger.error).toHaveBeenCalled();
    });

    it('should publish initial progress', async () => {
      await service.createParseSession('content', 'source', 'user-id');

      expect(redis.publish).toHaveBeenCalledWith(
        expect.stringContaining('parse:progress:'),
        expect.stringContaining('"step":"initializing"')
      );
      expect(cacheService.set).toHaveBeenCalledWith(
        expect.stringContaining('progress:'),
        expect.objectContaining({
          step: 'initializing',
          progress: 0,
          message: 'Initializing...',
        }),
        { ttl: 300 }
      );
    });
  });

  describe('getSession', () => {
    it('should return parsed session data', async () => {
      const mockData = {
        id: 'session-123',
        import_status: 'success',
        parsed_data: { title: 'Test Trip' },
        error_message: null,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:01:00Z',
      };

      mockSupabase.single.mockResolvedValueOnce({ data: mockData, error: null });

      const session = await service.getSession('session-123');

      expect(session).toMatchObject({
        id: 'session-123',
        status: 'complete',
        progress: 100,
        currentStep: 'complete',
        result: { title: 'Test Trip' },
        error: undefined,
      });
    });

    it('should return null for non-existent session', async () => {
      mockSupabase.single.mockResolvedValueOnce({ data: null, error: { message: 'Not found' } });

      const session = await service.getSession('non-existent');

      expect(session).toBeNull();
    });

    it('should map status correctly', async () => {
      const statuses = [
        { import_status: 'success', expected: 'complete' },
        { import_status: 'failed', expected: 'error' },
        { import_status: 'processing', expected: 'processing' },
        { import_status: 'other', expected: 'pending' },
      ];

      for (const { import_status, expected } of statuses) {
        mockSupabase.single.mockResolvedValueOnce({
          data: {
            id: 'test',
            import_status,
            created_at: new Date().toISOString(),
          },
          error: null,
        });

        const session = await service.getSession('test');
        expect(session?.status).toBe(expected);
      }
    });
  });

  describe('publishProgress', () => {
    it('should publish to Redis and cache', async () => {
      // Access private method through any type
      const publishProgress = (service as any).publishProgress.bind(service);

      await publishProgress('session-123', 'parsing', 50, 'Parsing activities...');

      expect(redis.publish).toHaveBeenCalledWith(
        'parse:progress:session-123',
        expect.stringContaining('"step":"parsing"')
      );

      expect(cacheService.set).toHaveBeenCalledWith(
        'progress:session-123',
        expect.objectContaining({
          sessionId: 'session-123',
          step: 'parsing',
          progress: 50,
          message: 'Parsing activities...',
        }),
        { ttl: 300 }
      );
    });

    it('should not throw on Redis error', async () => {
      vi.mocked(redis.publish).mockRejectedValueOnce(new Error('Redis error'));

      const publishProgress = (service as any).publishProgress.bind(service);

      // Should not throw
      await expect(
        publishProgress('session-123', 'error', 0, 'Error message')
      ).resolves.not.toThrow();

      expect(logger.error).toHaveBeenCalledWith(
        'Failed to publish progress',
        expect.objectContaining({ sessionId: 'session-123' })
      );
    });
  });

  describe('callAIAPI', () => {
    beforeEach(() => {
      process.env.OPENROUTER_API_KEY = 'test-api-key';
    });

    it('should handle successful API response', async () => {
      const mockResponse = {
        data: {
          choices: [{
            message: {
              content: JSON.stringify({
                title: 'Paris Trip',
                activities: [],
              })
            }
          }]
        }
      };

      vi.mocked(axios).post.mockResolvedValueOnce(mockResponse);

      const callAIAPI = (service as any).callAIAPI.bind(service);
      const result = await callAIAPI('content', 'chatgpt', 'model-id');

      expect(result).toMatchObject({
        title: 'Paris Trip',
        activities: [],
      });

      expect(axios.post).toHaveBeenCalledWith(
        'https://openrouter.ai/api/v1/chat/completions',
        expect.objectContaining({
          model: 'test-model-id', // Using the mocked model config
          temperature: 0.3,
          max_tokens: 4000,
          messages: expect.any(Array),
          response_format: { type: 'json_object' }
        }),
        expect.objectContaining({
          headers: expect.objectContaining({
            'Authorization': 'Bearer test-api-key',
            'Content-Type': 'application/json',
          }),
          timeout: 30000,
        })
      );
    });

    it('should handle rate limit error (429)', async () => {
      vi.mocked(axios).post.mockRejectedValueOnce({
        isAxiosError: true,
        response: { status: 429 },
      });
      vi.mocked(axios).isAxiosError.mockReturnValueOnce(true);

      const callAIAPI = (service as any).callAIAPI.bind(service);

      await expect(
        callAIAPI('content', 'source', 'model')
      ).rejects.toThrow('Rate limit exceeded. Please try again later.');
    });

    it('should handle timeout error', async () => {
      vi.mocked(axios).post.mockRejectedValueOnce({
        isAxiosError: true,
        code: 'ECONNABORTED',
      });
      vi.mocked(axios).isAxiosError.mockReturnValueOnce(true);

      const callAIAPI = (service as any).callAIAPI.bind(service);

      await expect(
        callAIAPI('content', 'source', 'model')
      ).rejects.toThrow('Request timed out. Please try with a shorter conversation.');
    });

    it('should handle missing response', async () => {
      vi.mocked(axios).post.mockResolvedValueOnce({
        data: { choices: [] }
      });

      const callAIAPI = (service as any).callAIAPI.bind(service);

      await expect(
        callAIAPI('content', 'source', 'model')
      ).rejects.toThrow('No response from AI');
    });

    it('should handle invalid JSON response', async () => {
      vi.mocked(axios).post.mockResolvedValueOnce({
        data: {
          choices: [{
            message: { content: 'Invalid JSON' }
          }]
        }
      });

      const callAIAPI = (service as any).callAIAPI.bind(service);

      await expect(
        callAIAPI('content', 'source', 'model')
      ).rejects.toThrow();
    });
  });

  describe('enhanceWithGeocoding', () => {
    it('should enhance activities with geocoding data', async () => {
      vi.mocked(geocodingService).isAvailable.mockReturnValue(true);
      vi.mocked(geocodingService).geocodeBatch.mockResolvedValue(
        new Map([
          ['Paris', { formatted: 'Paris, France', lat: 48.8566, lng: 2.3522 }],
          ['Eiffel Tower', { formatted: 'Eiffel Tower, Paris', lat: 48.8584, lng: 2.2945 }],
        ])
      );

      const enhanceWithGeocoding = (service as any).enhanceWithGeocoding.bind(service);
      const input = {
        destination: 'Paris',
        activities: [
          { location: { address: 'Eiffel Tower' } },
          { location: { address: 'Unknown Place' } },
        ],
      };

      const result = await enhanceWithGeocoding(input);

      expect(result.activities[0].location).toMatchObject({
        address: 'Eiffel Tower, Paris',
        lat: 48.8584,
        lng: 2.2945,
        confidence: 0.9,
      });

      // Unknown place should fallback to destination coords with variation
      expect(result.activities[1].location.lat).toBeCloseTo(48.8566, 0);
      expect(result.activities[1].location.lng).toBeCloseTo(2.3522, 0);
    });

    it('should use fallback coordinates when geocoding unavailable', async () => {
      vi.mocked(geocodingService).isAvailable.mockReturnValue(false);

      const enhanceWithGeocoding = (service as any).enhanceWithGeocoding.bind(service);
      const input = {
        destination: 'Paris',
        activities: [{ location: { address: 'Test' } }],
      };

      const result = await enhanceWithGeocoding(input);

      // Should use Paris fallback coordinates
      expect(result.activities[0].location.lat).toBeCloseTo(48.8566, 0);
      expect(result.activities[0].location.lng).toBeCloseTo(2.3522, 0);
    });
  });

  describe('parseAsync flow', () => {
    it('should publish all progress steps in sequence', async () => {
      const mockParsedData = {
        title: 'Test Trip',
        description: 'A test trip',
        startDate: '2024-01-01',
        endDate: '2024-01-05',
        destination: 'Paris',
        activities: [],
        metadata: {
          source: 'chatgpt',
          confidence: 0.9,
          totalDays: 5,
          totalActivities: 0,
          warnings: []
        }
      };

      vi.mocked(axios).post.mockResolvedValueOnce({
        data: {
          choices: [{
            message: { content: JSON.stringify(mockParsedData) }
          }]
        }
      });

      vi.mocked(geocodingService).isAvailable.mockReturnValue(false);

      // Mock database update for success case
      mockSupabase.update.mockReturnValue({
        eq: vi.fn().mockResolvedValue({ data: {}, error: null })
      });

      // Call parseAsync through createParseSession
      await service.createParseSession('content', 'source', 'user-id');

      // Wait for async processing with a longer timeout
      await new Promise(resolve => setTimeout(resolve, 200));

      // Check progress updates were published
      const publishCalls = vi.mocked(redis.publish).mock.calls;
      const progressSteps = publishCalls
        .filter(call => call[0].includes('parse:progress:'))
        .map(call => JSON.parse(call[1]).step);

      expect(progressSteps).toContain('initializing');
      expect(progressSteps).toContain('extracting');
      expect(progressSteps).toContain('enhancing');
      expect(progressSteps).toContain('validating');
      expect(progressSteps).toContain('finalizing');
      expect(progressSteps).toContain('complete');
    });

    it('should update database on parse error', async () => {
      vi.mocked(axios).post.mockRejectedValueOnce(new Error('API Error'));

      await service.createParseSession('content', 'source', 'user-id');

      // Wait for async processing
      await new Promise(resolve => setTimeout(resolve, 100));

      expect(mockSupabase.update).toHaveBeenCalledWith(
        expect.objectContaining({
          import_status: 'failed',
          error_message: 'API Error',
        })
      );

      // Should publish error progress
      const errorPublish = vi.mocked(redis.publish).mock.calls.find(
        call => call[1].includes('"step":"error"')
      );
      expect(errorPublish).toBeDefined();
    });
  });
});