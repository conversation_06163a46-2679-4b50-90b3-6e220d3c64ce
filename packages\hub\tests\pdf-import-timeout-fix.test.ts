import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { GeminiService } from '../src/services/gemini.service';
import { getAIParserService } from '../src/services/ai-parser.service';
import { estimateContentComplexity, selectOptimalModel, getFallbackModels } from '../src/config/ai.config';
import axios from 'axios';

// Mock axios
vi.mock('axios');
const mockedAxios = vi.mocked(axios);

describe('PDF Import Timeout Fix', () => {
  let geminiService: GeminiService;

  beforeEach(() => {
    vi.clearAllMocks();
    process.env.GOOGLE_GEMINI_API_KEY = 'test-key';
    geminiService = GeminiService.getInstance();
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  describe('Gemini Service Improvements', () => {
    it('should use increased maxOutputTokens (8192)', async () => {
      const mockResponse = {
        data: {
          candidates: [{
            content: {
              parts: [{
                text: JSON.stringify({
                  title: 'Test Trip',
                  destination: 'Paris',
                  activities: [
                    { title: 'Visit Eiffel Tower', type: 'sightseeing', day: 1 }
                  ]
                })
              }]
            }
          }],
          usageMetadata: {
            promptTokenCount: 100,
            candidatesTokenCount: 200,
            totalTokenCount: 300
          }
        }
      };

      mockedAxios.post.mockResolvedValueOnce(mockResponse);

      await geminiService.parseWithGemini('test content', 'test prompt');

      expect(mockedAxios.post).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({
          generationConfig: expect.objectContaining({
            maxOutputTokens: 8192 // Verify increased token limit
          })
        }),
        expect.any(Object)
      );
    });

    it('should detect and handle truncated JSON responses', async () => {
      const truncatedResponse = {
        data: {
          candidates: [{
            content: {
              parts: [{
                text: '{"title": "Test Trip", "activities": [{"title": "Visit"' // Truncated
              }]
            }
          }]
        }
      };

      mockedAxios.post.mockResolvedValueOnce(truncatedResponse);

      await expect(geminiService.parseWithGemini('test content', 'test prompt'))
        .rejects.toThrow(/No JSON found|Failed to parse/i);
    });

    it('should handle incomplete JSON with proper error message', async () => {
      const incompleteResponse = {
        data: {
          candidates: [{
            content: {
              parts: [{
                text: '{"title": "Test Trip", "activities": ['
              }]
            }
          }]
        }
      };

      mockedAxios.post.mockResolvedValueOnce(incompleteResponse);

      await expect(geminiService.parseWithGemini('test content', 'test prompt'))
        .rejects.toThrow(/No JSON found|Failed to parse/i);
    });

    it('should successfully parse valid complete JSON', async () => {
      const validResponse = {
        data: {
          candidates: [{
            content: {
              parts: [{
                text: JSON.stringify({
                  title: 'European Adventure',
                  destination: 'Europe',
                  startDate: '2024-06-01',
                  endDate: '2024-06-15',
                  activities: [
                    {
                      title: 'Visit Eiffel Tower',
                      type: 'sightseeing',
                      location: 'Paris, France',
                      day: 1,
                      startTime: '10:00',
                      price: 25,
                      currency: 'EUR'
                    },
                    {
                      title: 'Sagrada Familia Tour',
                      type: 'sightseeing',
                      location: 'Barcelona, Spain',
                      day: 5,
                      startTime: '14:00',
                      price: 30,
                      currency: 'EUR'
                    }
                  ]
                })
              }]
            }
          }],
          usageMetadata: {
            promptTokenCount: 500,
            candidatesTokenCount: 800,
            totalTokenCount: 1300
          }
        }
      };

      mockedAxios.post.mockResolvedValueOnce(validResponse);

      const result = await geminiService.parseWithGemini('complex itinerary content', 'parse this trip');

      expect(result).toEqual({
        title: 'European Adventure',
        destination: 'Europe',
        startDate: '2024-06-01',
        endDate: '2024-06-15',
        activities: [
          {
            title: 'Visit Eiffel Tower',
            type: 'sightseeing',
            location: 'Paris, France',
            day: 1,
            startTime: '10:00',
            price: 25,
            currency: 'EUR'
          },
          {
            title: 'Sagrada Familia Tour',
            type: 'sightseeing',
            location: 'Barcelona, Spain',
            day: 5,
            startTime: '14:00',
            price: 30,
            currency: 'EUR'
          }
        ]
      });
    });
  });

  describe('AI Parser Service Session Management', () => {
    it('should update session status during parsing steps', async () => {
      const aiParserService = getAIParserService();
      
      // Mock the session creation and status updates
      const mockSession = {
        id: 'test-session-id',
        status: 'processing',
        progress: 0,
        currentStep: 'initializing'
      };

      // Test that session status is properly tracked
      expect(aiParserService).toBeDefined();
      expect(typeof aiParserService.createParseSession).toBe('function');
      expect(typeof aiParserService.getSession).toBe('function');
    });
  });

  describe('Dynamic Model Selection', () => {
    it('should correctly estimate content complexity', () => {
      const simpleContent = 'Day 1: Visit Eiffel Tower at 10:00 AM\nDay 2: Louvre Museum';
      const complexContent = `
        Day 1: London Adventure
        9:00 AM: Arrive at Heathrow Airport
        11:00 AM: Check into hotel
        2:00 PM: Tower of London tour
        7:00 PM: Dinner at restaurant

        Day 2: Museums and Culture
        9:00 AM: British Museum
        1:00 PM: Lunch break
        3:00 PM: Tate Modern
        8:00 PM: West End show

        Day 3: Parks and Shopping
        10:00 AM: Hyde Park walk
        12:00 PM: Oxford Street shopping
        4:00 PM: Afternoon tea
        7:00 PM: Covent Garden

        Day 4: Day trip to Windsor
        8:00 AM: Train to Windsor
        10:00 AM: Windsor Castle tour
        2:00 PM: Windsor Great Park
        6:00 PM: Return to London

        Day 5: Greenwich and Thames
        9:00 AM: Greenwich Observatory
        12:00 PM: Maritime Museum
        3:00 PM: Thames cruise
        6:00 PM: Canary Wharf
      `.repeat(3); // Make it very long

      expect(estimateContentComplexity(simpleContent)).toBe('simple');
      expect(estimateContentComplexity(complexContent)).toBe('very_complex');
    });

    it('should select appropriate models based on complexity', () => {
      const simpleContent = 'Day 1: Visit Paris\nDay 2: Return home';
      const complexContent = 'Day 1: Start of 15-day European adventure...' + 'x'.repeat(10000);

      expect(selectOptimalModel(simpleContent)).toBe('gemini-flash-2.0');
      expect(selectOptimalModel(complexContent)).toBe('kimi-k2-free'); // Complex content uses Kimi-K2
    });

    it('should provide appropriate fallback models for different complexities', () => {
      const simpleFallbacks = getFallbackModels('simple');
      const complexFallbacks = getFallbackModels('very_complex');

      expect(simpleFallbacks).toContain('kimi-k2-free');
      expect(complexFallbacks).toContain('kimi-k2-free');
      expect(complexFallbacks[0]).toBe('kimi-k2-free'); // Kimi should be first fallback for very complex
    });
  });

  describe('Error Handling Improvements', () => {
    it('should provide specific error messages for different failure modes', async () => {
      // Test timeout error
      mockedAxios.post.mockRejectedValueOnce({
        code: 'ECONNABORTED',
        message: 'timeout of 60000ms exceeded'
      });

      await expect(geminiService.parseWithGemini('test content', 'test prompt'))
        .rejects.toThrow();

      // Test rate limit error
      mockedAxios.post.mockRejectedValueOnce({
        isAxiosError: true,
        response: { status: 429 }
      });

      await expect(geminiService.parseWithGemini('test content', 'test prompt'))
        .rejects.toThrow(/rate limit/i);

      // Test invalid API key error
      mockedAxios.post.mockRejectedValueOnce({
        isAxiosError: true,
        response: { status: 403 }
      });

      await expect(geminiService.parseWithGemini('test content', 'test prompt'))
        .rejects.toThrow(/invalid.*api key/i);
    });
  });
});
